#!/bin/bash

BASE_URL="https://preview.linki.group"

# Create directories
mkdir -p assets/towerRush/img
mkdir -p assets/towerRush/css
mkdir -p assets/towerRush/js
mkdir -p assets/general/images/effects

echo "Downloading CSS files..."
curl -o assets/towerRush/css/style.css "${BASE_URL}/assets/towerRush/css/style.css?v=1.4.3"

echo "Downloading JS files..."
curl -o assets/towerRush/js/app.min.js "${BASE_URL}/assets/towerRush/js/app.min.js?v=1.4.3"
curl -o assets/general/common.min.js "${BASE_URL}/assets/general/common.min.js?v=1.4.3"
curl -o assets/general/commonBonuses.min.js "${BASE_URL}/assets/general/commonBonuses.min.js?v=1.4.3"

echo "Downloading images..."
curl -o assets/towerRush/img/logo.webp "${BASE_URL}/assets/towerRush/img/logo.webp"
curl -o assets/towerRush/img/shield.webp "${BASE_URL}/assets/towerRush/img/shield.webp"
curl -o assets/towerRush/img/build-1.webp "${BASE_URL}/assets/towerRush/img/build-1.webp"
curl -o assets/towerRush/img/build-2.webp "${BASE_URL}/assets/towerRush/img/build-2.webp"
curl -o assets/towerRush/img/build-3.webp "${BASE_URL}/assets/towerRush/img/build-3.webp"
curl -o assets/towerRush/img/build-4.webp "${BASE_URL}/assets/towerRush/img/build-4.webp"
curl -o assets/towerRush/img/modal-win.webp "${BASE_URL}/assets/towerRush/img/modal-win.webp"

echo "Downloading icons..."
curl -o assets/towerRush/img/apple-touch-icon.webp "${BASE_URL}/assets/towerRush/img/apple-touch-icon.webp"
curl -o assets/towerRush/img/favicon-32x32.webp "${BASE_URL}/assets/towerRush/img/favicon-32x32.webp"
curl -o assets/towerRush/img/favicon-16x16.webp "${BASE_URL}/assets/towerRush/img/favicon-16x16.webp"
curl -o assets/towerRush/img/safari-pinned-tab.svg "${BASE_URL}/assets/towerRush/img/safari-pinned-tab.svg"

echo "Downloading effects..."
curl -o assets/general/images/effects/desctop_boom.gif "${BASE_URL}/assets/general/images/effects/desctop_boom.gif"

echo "Downloading manifest..."
curl -o manifest.json "${BASE_URL}/manifest.json"

echo "Creating fixed reg-sw.js..."
cat > reg-sw.js << 'EOF'
// Service Worker Registration - Fixed for local development
console.log('Service Worker registration disabled for local development');

if ('serviceWorker' in navigator && window.location.protocol === 'https:') {
    navigator.serviceWorker.register('/sw.js')
        .then(function(registration) {
            console.log('ServiceWorker registration successful');
        })
        .catch(function(err) {
            console.log('ServiceWorker registration failed: ', err);
        });
} else {
    console.log('Service Worker not supported or running on HTTP - skipping registration');
}
EOF

echo "All files downloaded successfully!"
