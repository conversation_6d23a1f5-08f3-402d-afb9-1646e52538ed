#!/bin/bash

echo "🎮 Starting Magic Quiz Game Server..."
echo "📁 Serving files from: $(pwd)"
echo "🌐 Game will be available at: http://localhost:8000"
echo "🔗 To set claim link, open browser console and use:"
echo "   ClaimLinkConfig.setLink('https://your-link.com')"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start Python HTTP server
if command -v python3 &> /dev/null; then
    python3 -m http.server 8000
elif command -v python &> /dev/null; then
    python -m SimpleHTTPServer 8000
else
    echo "❌ Python not found. Please install Python to run the server."
    echo "Alternative: Use any other HTTP server to serve this directory"
    exit 1
fi
