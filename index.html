<!DOCTYPE html>
<html lang="ua">
<head>
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=0.9, maximum-scale=0.9, minimum-scale=0.9, viewport-fit=cover">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" data-qmeta="equiv">
    <meta name="theme-color" content="#FFFFFF">
    <meta name="google" value="notranslate">
    <meta name="Description" content="Magic Quiz Ngeriya
">
    <title>Magic Quiz Ngeriya</title>
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/towerRush/img/apple-touch-icon.webp">
    <link rel="icon" type="image/png" sizes="32x32" href="/assets/towerRush/img/favicon-32x32.webp">
    <link rel="icon" type="image/png" sizes="16x16" href="/assets/towerRush/img/favicon-16x16.webp">
    <link rel="mask-icon" href="/assets/towerRush/img/safari-pinned-tab.svg" color="#5bbad5">
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="theme-color" content="#ffffff">
    <meta name="apple-mobile-web-app-title" content="Magic Quiz Ngeriya">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <link href="/assets/towerRush/css/style.css?v=1.4.3" rel="stylesheet">

    <link rel="manifest" href="./manifest.json">




<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">


<style>
    body {
        font-family: 'Poppins', sans-serif;
        font-weight: 800;
    }
</style>

    <style>
        .loader {
            background: linear-gradient(180deg, #8A91A1 22.36%, #314353 87.25%);
        }
        .loader__logo {
            max-width: 350px !important;
            @media (max-width: 767px) {
                margin-top: -25% !important;
                max-width: 230px !important;
            }
        }
        .loader__progress {
            background: #000000;
        }
        .loader__progress-line {
            min-width: 20px;
            border: 2px solid #FFF17F;
            background: linear-gradient(to right, #AE6D00, #FBDF00);
        }
    </style>
    <script src="./reg-sw.js"></script>

</head>
<body>
<style>
.loader {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 999;
    width: 100%;
    height: 100vh;
    padding: 15px;
    display: flex;
}
.loader.is--loaded {
    display: none;
}
.loader__logo {
    margin-bottom: 35px;
    width: 100%;
    max-width: 400px;
}
.loader__progress {
    width: 100%;
    height: 28px;
    max-width: 340px;
    padding: 4px;
    border-radius: 100px;
    position: relative;
}
.loader__progress::before {
    position: absolute;
    content: '';
    left: 4px;
    top: 4px;
    border-radius: 100px;
    width: calc(100% - 8px);
    height: calc(100% - 8px);
    background-color: #fff;
}
.loader__progress-line {
    position: relative;
    z-index: 2;
    width: 0;
    height: 100%;
    border-radius: 100px;
    transition: width 0.5s ease;
}
@media (max-width: 767px) {
    .loader__progress {
        max-width: 220px;
    }
}
</style>
<div class="loader">
    <img class="loader__logo" src="/assets/towerRush/img/logo.webp" alt="logo">
    <div class="loader__progress">
        <div class="loader__progress-line"></div>
    </div>
</div>
<script>
  let progressInterval = null;
  const progressLine = document.querySelector('.loader__progress-line');
  const loader = document.querySelector('.loader');

  function increaseLoaderProgress() {
    let width = 0;
    progressInterval = setInterval(() => {
      if (width >= 99) {
        progressLine.style.width = '100%';
      } else {
        width += 2;
        progressLine.style.width = width + '%';
      }
    }, 1000);
  }

  increaseLoaderProgress();

  function addLoadedClass() {
    loader.classList.add('is--loaded');
  }

  function setLoaderProgressTo100() {
    setTimeout(() => {
      progressLine.style.width = '100%';
      clearInterval(progressInterval);
      setTimeout(() => {
        addLoadedClass();
      }, 800)
    }, 600)
  }

  window.addEventListener('load', setLoaderProgressTo100);
</script>



<div class="body-wrapper">
    <div class="logo">
        <img src="/assets/towerRush/img/logo.webp" alt="">
    </div>

    <div class="shield">
        <img src="/assets/towerRush/img/shield.webp" alt="">
    </div>

    <div class="game" id="game" data-rate="2000000">
        <div class="game__field" id="game-field">
            <div class="game__field-street"></div>
            <div class="game__field-cloud"></div>

            <div class="game__field-results" id="game-results">
                <p class="game__field-results-label">Results</p>
            </div>

            <div class="game__field-multiplier" >
                x<span id="game-multiplier"></span>
            </div>

            <div class="game__field-tower" id="game-tower">
                <div class="game__field-tower-hook"></div>

                <div class="game__field-tower-item is--0"></div>
                <div class="game__field-tower-item is--1">
                    <div class="game__field-tower-item-build">
                        <img src="/assets/towerRush/img/build-1.webp" alt="">
                    </div>
                </div>
                <div class="game__field-tower-item is--2">
                    <div class="game__field-tower-item-build">
                        <img src="/assets/towerRush/img/build-2.webp" alt="">
                    </div>
                </div>
                <div class="game__field-tower-item is--3">
                    <div class="game__field-tower-item-build">
                        <img src="/assets/towerRush/img/build-3.webp" alt="">
                    </div>
                </div>
                <div class="game__field-tower-item is--4">
                    <div class="game__field-tower-item-build">
                        <img src="/assets/towerRush/img/build-4.webp" alt="">
                    </div>
                </div>
            </div>
        </div>

        <div class="game__controls">
            <button class="game__controls-cash" type="button" id="game-cash">
                <span class="game__controls-cash-label">
                    Your current balance
                </span>
                <span class="game__controls-cash-value">
                    <span id="game-balance">0</span> USD
                </span>
            </button>
            <div class="game__controls-go">

    <button class="bottom__section-button pulse" id="go-btn" data-spins="1">
        <span class="bottom__section-button-default" id="go-btn-default-text"> Play for Free</span>
        <span class="bottom__section-button-next" id="go-btn-next-text">_</span>
    </button>


            </div>
        </div>
    </div>


        <div class="modal" id="modal">
            <div class="modal__content">
                <div class="modal__logo">
                    <img src="/assets/towerRush/img/logo.webp" alt="">
                </div>

                <div class="modal__img">
                    <img src="/assets/towerRush/img/modal-win.webp" alt="">
                </div>

                <div class="modal__win-btn">

    <button type="button" id="win-button-modal" class="bottom__section-button is--link">
        <span>Claim Now</span>
    </button>


                </div>
            </div>
        </div>


    <div class="effects hidden" id="effects">
        <img loading="lazy" class="effects__image hidden" id="effects-image" src="/assets/general/images/effects/desctop_boom.gif" alt="red_flare">
    </div>
</div>

<div class="devMode dev-mode" id="devMode">
    <button class="menu-button" id="toggleMenu">
        <svg xmlns="http://www.w3.org/2000/svg" width="30px" height="30px" viewBox="0 0 20 20" fill="none" class="open">
            <path fill="currentColor" fill-rule="evenodd" d="M19 4a1 1 0 01-1 1H2a1 1 0 010-2h16a1 1 0 011 1zm0 6a1 1 0 01-1 1H2a1 1 0 110-2h16a1 1 0 011 1zm-1 7a1 1 0 100-2H2a1 1 0 100 2h16z"/>
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" class="close" viewBox="0 0 384 512"><!--!Font Awesome Free 6.6.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2024 Fonticons, Inc.--><path d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"/></svg>
    </button>
    <div class="dev-mode__wrapper" id="settingsWrapper">
        <form id="settingsForm">
            <textarea id="settingsTextarea" rows="10" cols="30"></textarea>
            <br>
            <button type="button" id="getSettings">Отримати</button>
            <button type="button" id="submitSettings">Оновити</button>
        </form>
    </div>
</div>
<style>
    .dev-mode {
        visibility: hidden;
        display: none;
    }
    .dev-mode.is--active {
        visibility: visible;
        display: flex;
    }
    .dev-mode .menu-button  {
        font-size: 20px;
        color: #fff;
        background: transparent;
        box-shadow: none;
        border: none;
        position: fixed;
        right: 10px;
        top: 10px;
        z-index: 999;
        cursor: pointer;
    }
    .dev-mode .menu-button svg {
        fill: #fff;
        width: 30px;
        height: 30px;
    }
    .dev-mode .menu-button .close {
        display: none;
    }
    .dev-mode .menu-button.is--open .close {
        display: flex;
    }
    .dev-mode .menu-button.is--open .open {
        display: none;
    }
    .dev-mode {
    }
    .dev-mode__wrapper {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        align-items: center;
        justify-content: center;
        z-index: 998;
        display: none;
    }
    .dev-mode__wrapper.is--open {
        display: flex;
    }
    .dev-mode__wrapper:before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0, 0.5);
        z-index: -1;
    }
    .dev-mode__wrapper form {
        width: 100%;
        padding: 30px;
    }
    .dev-mode__wrapper textarea {
        width: 100%;
        height: 500px;
        margin-bottom: 15px;
        font-size: 20px;
    }
    .dev-mode__wrapper button {
        background-color: #4CAF50;
        color: white;
        padding: 10px 20px;
        font-size: 16px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: background-color 0.3s ease, transform 0.2s ease;
    }
</style>
<script>
  function checkDevMode() {
    const urlParams = new URLSearchParams(window.location.search);
    const devMode = urlParams.get('devMode');
    const devModeContainer = document.getElementById('devMode');

    if (devMode === 'true') {
      devModeContainer.classList.add('is--active');
    } else if (devMode === 'false') {
      devModeContainer.classList.remove('is--active');
    }
  }
  document.getElementById('toggleMenu').addEventListener('click', () => {
    const menuButton = document.getElementById('toggleMenu');
    const menu = document.getElementById('settingsWrapper');
    menuButton.classList.toggle('is--open');
    menu.classList.toggle('is--open');
  });
  document.getElementById('getSettings').addEventListener('click', async () => {
    try {
      const response = await fetch('/settings');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      document.getElementById('settingsTextarea').value = JSON.stringify(data, null, 2);
    } catch (error) {
      console.error('Error fetching settings:', error);
    }
  });
  document.getElementById('submitSettings').addEventListener('click', async () => {
    try {
      const settings = document.getElementById('settingsTextarea').value;
      const response = await fetch('/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: settings
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      window.location.reload();
    } catch (error) {
      console.error('Error:', error);
    }
  });
  window.addEventListener('DOMContentLoaded', checkDevMode);
</script>

<script src="./assets/towerRush/js/app.min.js"></script>
<script src="./assets/general/common.min.js"></script>

    <script>
      window.keitaro = false
    </script>



    <script>
      window.template = 'towerRush';
    </script>









    <script defer src="./assets/general/commonBonuses.min.js"></script>

    <!-- Link Configuration System -->
    <script>
        // Configuration for Claim Now button link
        window.ClaimLinkConfig = {
            // Default link if none is set
            defaultLink: 'https://example.com',

            // Set the claim link
            setLink: function(url) {
                if (!url || typeof url !== 'string') {
                    console.error('Invalid URL provided');
                    return false;
                }
                localStorage.setItem('claimLink', url);
                console.log('Claim link set to:', url);
                return true;
            },

            // Get the current claim link
            getLink: function() {
                return localStorage.getItem('claimLink') || this.defaultLink;
            },

            // Remove the claim link (revert to default)
            removeLink: function() {
                localStorage.removeItem('claimLink');
                console.log('Claim link reset to default');
            }
        };

        // Override the claim button functionality
        document.addEventListener('DOMContentLoaded', function() {
            const claimButton = document.getElementById('win-button-modal');
            if (claimButton) {
                claimButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    const claimLink = window.ClaimLinkConfig.getLink();
                    console.log('Opening claim link:', claimLink);
                    window.open(claimLink, '_blank');

                    // Hide modal
                    const modal = document.getElementById('modal');
                    if (modal) {
                        modal.classList.remove('is--active');
                    }
                });
            }
        });

        // Console helper functions for easy configuration
        console.log('%c🎮 Magic Quiz Game - Link Configuration', 'color: #FFD700; font-size: 16px; font-weight: bold;');
        console.log('%cTo set the claim link, use: ClaimLinkConfig.setLink("https://your-link.com")', 'color: #4CAF50;');
        console.log('%cTo get current link, use: ClaimLinkConfig.getLink()', 'color: #2196F3;');
        console.log('%cTo reset to default, use: ClaimLinkConfig.removeLink()', 'color: #FF9800;');
    </script>

</body>
</html>