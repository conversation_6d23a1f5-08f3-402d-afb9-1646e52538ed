// Service Worker Registration - Fixed for local development
console.log('Service Worker registration disabled for local development');

if ('serviceWorker' in navigator && window.location.protocol === 'https:') {
    navigator.serviceWorker.register('/sw.js')
        .then(function(registration) {
            console.log('ServiceWorker registration successful');
        })
        .catch(function(err) {
            console.log('ServiceWorker registration failed: ', err);
        });
} else {
    console.log('Service Worker not supported or running on HTTP - skipping registration');
}
