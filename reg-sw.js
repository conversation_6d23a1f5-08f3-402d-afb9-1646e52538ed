// Service Worker Registration - Fixed for local development
// This file replaces the problematic service worker registration

console.log('Service Worker registration disabled for local development');

// Instead of registering a service worker, we'll just log that it's disabled
// This prevents the "Failed to register a ServiceWorker" error when running locally

if ('serviceWorker' in navigator && window.location.protocol === 'https:') {
    // Only register service worker on HTTPS in production
    navigator.serviceWorker.register('/sw.js')
        .then(function(registration) {
            console.log('ServiceWorker registration successful');
        })
        .catch(function(err) {
            console.log('ServiceWorker registration failed: ', err);
        });
} else {
    console.log('Service Worker not supported or running on HTTP - skipping registration');
}
