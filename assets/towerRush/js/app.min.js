// Tower Rush Game Logic
(function() {
    'use strict';
    
    let gameState = {
        balance: 0,
        multiplier: 1,
        isPlaying: false,
        currentLevel: 0,
        maxLevel: 4
    };
    
    // DOM Elements
    const gameElement = document.getElementById('game');
    const gameField = document.getElementById('game-field');
    const gameResults = document.getElementById('game-results');
    const gameMultiplier = document.getElementById('game-multiplier');
    const gameBalance = document.getElementById('game-balance');
    const goBtn = document.getElementById('go-btn');
    const goBtnDefaultText = document.getElementById('go-btn-default-text');
    const goBtnNextText = document.getElementById('go-btn-next-text');
    const modal = document.getElementById('modal');
    const winButtonModal = document.getElementById('win-button-modal');
    const effects = document.getElementById('effects');
    const effectsImage = document.getElementById('effects-image');
    
    // Initialize game
    function initGame() {
        updateBalance(0);
        updateMultiplier(1);
        updateButtonText('Play for Free');
        
        // Add event listeners
        if (goBtn) {
            goBtn.addEventListener('click', playGame);
        }
        
        if (winButtonModal) {
            winButtonModal.addEventListener('click', claimReward);
        }
        
        console.log('Tower Rush Game initialized');
    }
    
    // Update balance display
    function updateBalance(amount) {
        gameState.balance = amount;
        if (gameBalance) {
            gameBalance.textContent = gameState.balance.toLocaleString();
        }
    }
    
    // Update multiplier display
    function updateMultiplier(mult) {
        gameState.multiplier = mult;
        if (gameMultiplier) {
            gameMultiplier.textContent = mult + 'x';
        }
    }
    
    // Update button text
    function updateButtonText(text) {
        if (goBtnDefaultText) {
            goBtnDefaultText.textContent = text;
        }
    }
    
    // Play game animation
    function playGame() {
        if (gameState.isPlaying) return;
        
        gameState.isPlaying = true;
        goBtn.disabled = true;
        
        // Animate tower building
        animateTowerBuilding();
        
        // Generate random result
        setTimeout(() => {
            const winAmount = Math.floor(Math.random() * 1000000) + 100000;
            const multiplier = Math.floor(Math.random() * 10) + 2;
            
            gameState.currentLevel = Math.min(gameState.currentLevel + 1, gameState.maxLevel);
            
            updateBalance(winAmount);
            updateMultiplier(multiplier);
            
            // Show win modal
            showWinModal();
            
            gameState.isPlaying = false;
            goBtn.disabled = false;
            updateButtonText('Play Again');
        }, 3000);
    }
    
    // Animate tower building
    function animateTowerBuilding() {
        const towerItems = document.querySelectorAll('.game__field-tower-item');
        let currentItem = 0;
        
        const buildInterval = setInterval(() => {
            if (currentItem < towerItems.length) {
                const item = towerItems[currentItem];
                if (item) {
                    item.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        item.style.transform = 'scale(1)';
                    }, 200);
                }
                currentItem++;
            } else {
                clearInterval(buildInterval);
                showEffects();
            }
        }, 500);
    }
    
    // Show effects
    function showEffects() {
        if (effects && effectsImage) {
            effects.classList.remove('hidden');
            effectsImage.classList.remove('hidden');
            
            setTimeout(() => {
                effects.classList.add('hidden');
                effectsImage.classList.add('hidden');
            }, 2000);
        }
    }
    
    // Show win modal
    function showWinModal() {
        if (modal) {
            modal.classList.add('is--active');
        }
    }
    
    // Hide win modal
    function hideWinModal() {
        if (modal) {
            modal.classList.remove('is--active');
        }
    }
    
    // Claim reward function
    function claimReward() {
        // Get the configured link from localStorage or use default
        const claimLink = localStorage.getItem('claimLink') || 'https://example.com';
        
        // Open the link in a new tab
        window.open(claimLink, '_blank');
        
        // Hide modal
        hideWinModal();
        
        console.log('Reward claimed, opening:', claimLink);
    }
    
    // Configuration function for setting claim link
    window.setClaimLink = function(url) {
        localStorage.setItem('claimLink', url);
        console.log('Claim link set to:', url);
    };
    
    // Get current claim link
    window.getClaimLink = function() {
        return localStorage.getItem('claimLink') || 'https://example.com';
    };
    
    // Initialize when DOM is loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initGame);
    } else {
        initGame();
    }
    
    // Export functions for global access
    window.TowerRushGame = {
        setClaimLink: window.setClaimLink,
        getClaimLink: window.getClaimLink,
        playGame: playGame,
        updateBalance: updateBalance,
        updateMultiplier: updateMultiplier
    };
    
})();
