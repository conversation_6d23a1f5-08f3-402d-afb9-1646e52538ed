/* Tower Rush Game Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    font-weight: 800;
    background: linear-gradient(180deg, #8A91A1 22.36%, #314353 87.25%);
    min-height: 100vh;
    overflow: hidden;
}

.body-wrapper {
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.logo {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.logo img {
    max-width: 200px;
    height: auto;
}

.shield {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 10;
}

.shield img {
    max-width: 60px;
    height: auto;
}

.game {
    width: 100%;
    max-width: 400px;
    height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
}

.game__field {
    flex: 1;
    position: relative;
    background: linear-gradient(180deg, #87CEEB 0%, #98FB98 100%);
    overflow: hidden;
}

.game__field-street {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 80px;
    background: #696969;
    border-top: 4px solid #FFD700;
}

.game__field-cloud {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    height: 60px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 30px;
}

.game__field-results {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 18px;
    font-weight: bold;
}

.game__field-multiplier {
    position: absolute;
    top: 80px;
    right: 20px;
    background: #FFD700;
    color: #000;
    padding: 8px 15px;
    border-radius: 15px;
    font-size: 20px;
    font-weight: bold;
}

.game__field-tower {
    position: absolute;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 300px;
}

.game__field-tower-hook {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 20px;
    background: #8B4513;
}

.game__field-tower-item {
    position: absolute;
    width: 100%;
    height: 60px;
    transition: all 0.3s ease;
}

.game__field-tower-item.is--0 { bottom: 240px; }
.game__field-tower-item.is--1 { bottom: 180px; }
.game__field-tower-item.is--2 { bottom: 120px; }
.game__field-tower-item.is--3 { bottom: 60px; }
.game__field-tower-item.is--4 { bottom: 0px; }

.game__field-tower-item-build {
    width: 100%;
    height: 100%;
    background: #8B4513;
    border: 2px solid #654321;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.game__field-tower-item-build img {
    max-width: 80%;
    max-height: 80%;
    object-fit: contain;
}

.game__controls {
    background: rgba(0, 0, 0, 0.9);
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.game__controls-cash {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    border: none;
    border-radius: 15px;
    padding: 15px;
    color: #000;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.game__controls-cash:hover {
    transform: scale(1.05);
}

.game__controls-cash-label {
    display: block;
    font-size: 12px;
    margin-bottom: 5px;
}

.game__controls-cash-value {
    display: block;
    font-size: 18px;
}

.bottom__section-button {
    background: linear-gradient(45deg, #FF6B6B, #FF8E53);
    border: none;
    border-radius: 25px;
    padding: 15px 30px;
    color: white;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.bottom__section-button:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

.bottom__section-button.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal.is--active {
    display: flex;
}

.modal__content {
    background: linear-gradient(180deg, #FFD700, #FFA500);
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    max-width: 90%;
    width: 350px;
}

.modal__logo {
    margin-bottom: 20px;
}

.modal__logo img {
    max-width: 150px;
    height: auto;
}

.modal__img {
    margin-bottom: 20px;
}

.modal__img img {
    max-width: 200px;
    height: auto;
}

.effects {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 999;
}

.effects.hidden {
    display: none;
}

.effects__image {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 300px;
    height: auto;
}

.hidden {
    display: none !important;
}

/* Responsive */
@media (max-width: 767px) {
    .logo img {
        max-width: 150px;
    }
    
    .shield img {
        max-width: 40px;
    }
    
    .game {
        max-width: 100%;
    }
    
    .game__field-tower {
        width: 100px;
        height: 250px;
    }
    
    .game__field-tower-item {
        height: 50px;
    }
    
    .game__field-tower-item.is--0 { bottom: 200px; }
    .game__field-tower-item.is--1 { bottom: 150px; }
    .game__field-tower-item.is--2 { bottom: 100px; }
    .game__field-tower-item.is--3 { bottom: 50px; }
    .game__field-tower-item.is--4 { bottom: 0px; }
}
