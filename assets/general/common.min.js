// Common utilities and functions - Fixed version
(function() {
    'use strict';
    
    // Utility functions
    const Utils = {
        // Random number generator
        random: function(min, max) {
            return Math.floor(Math.random() * (max - min + 1)) + min;
        },
        
        // Format number with commas
        formatNumber: function(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },
        
        // Animate element
        animate: function(element, property, from, to, duration, callback) {
            if (!element) return;
            
            const start = performance.now();
            const change = to - from;
            
            function step(timestamp) {
                const elapsed = timestamp - start;
                const progress = Math.min(elapsed / duration, 1);
                
                const value = from + (change * progress);
                element.style[property] = value + (property === 'opacity' ? '' : 'px');
                
                if (progress < 1) {
                    requestAnimationFrame(step);
                } else if (callback) {
                    callback();
                }
            }
            
            requestAnimationFrame(step);
        },
        
        // Show notification
        showNotification: function(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification--${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                z-index: 10000;
                font-weight: bold;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            
            document.body.appendChild(notification);
            
            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        },
        
        // Local storage helpers
        storage: {
            set: function(key, value) {
                try {
                    localStorage.setItem(key, JSON.stringify(value));
                    return true;
                } catch (e) {
                    console.error('Failed to save to localStorage:', e);
                    return false;
                }
            },
            
            get: function(key, defaultValue = null) {
                try {
                    const item = localStorage.getItem(key);
                    return item ? JSON.parse(item) : defaultValue;
                } catch (e) {
                    console.error('Failed to read from localStorage:', e);
                    return defaultValue;
                }
            },
            
            remove: function(key) {
                try {
                    localStorage.removeItem(key);
                    return true;
                } catch (e) {
                    console.error('Failed to remove from localStorage:', e);
                    return false;
                }
            }
        }
    };
    
    // Configuration management
    const Config = {
        // Default settings
        defaults: {
            claimLink: 'https://example.com',
            gameSettings: {
                minWin: 100000,
                maxWin: 1000000,
                minMultiplier: 2,
                maxMultiplier: 10
            }
        },
        
        // Get configuration value
        get: function(key) {
            return Utils.storage.get(key, this.defaults[key]);
        },
        
        // Set configuration value
        set: function(key, value) {
            return Utils.storage.set(key, value);
        },
        
        // Reset to defaults
        reset: function() {
            Object.keys(this.defaults).forEach(key => {
                this.set(key, this.defaults[key]);
            });
        }
    };
    
    // Event system
    const Events = {
        listeners: {},
        
        on: function(event, callback) {
            if (!this.listeners[event]) {
                this.listeners[event] = [];
            }
            this.listeners[event].push(callback);
        },
        
        off: function(event, callback) {
            if (!this.listeners[event]) return;
            const index = this.listeners[event].indexOf(callback);
            if (index > -1) {
                this.listeners[event].splice(index, 1);
            }
        },
        
        emit: function(event, data) {
            if (!this.listeners[event]) return;
            this.listeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (e) {
                    console.error('Error in event listener:', e);
                }
            });
        }
    };
    
    // Initialize common functionality
    function init() {
        // Set up global error handler
        window.addEventListener('error', function(e) {
            console.error('Global error caught:', e.error);
            Utils.showNotification('An error occurred. Please refresh the page.', 'error');
        });
        
        // Set up unhandled promise rejection handler
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled promise rejection:', e.reason);
            e.preventDefault();
        });
        
        console.log('Common utilities initialized');
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // Export to global scope
    window.Utils = Utils;
    window.Config = Config;
    window.Events = Events;
    
})();
