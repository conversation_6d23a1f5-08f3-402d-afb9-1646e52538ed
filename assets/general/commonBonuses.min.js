// Common Bonuses System
(function() {
    'use strict';
    
    const BonusSystem = {
        // Bonus types
        types: {
            MULTIPLIER: 'multiplier',
            CASH: 'cash',
            FREE_SPINS: 'free_spins'
        },
        
        // Active bonuses
        activeBonuses: [],
        
        // Add bonus
        addBonus: function(type, value, duration = 0) {
            const bonus = {
                id: Date.now() + Math.random(),
                type: type,
                value: value,
                duration: duration,
                startTime: Date.now()
            };
            
            this.activeBonuses.push(bonus);
            this.applyBonus(bonus);
            
            if (duration > 0) {
                setTimeout(() => {
                    this.removeBonus(bonus.id);
                }, duration);
            }
            
            Events.emit('bonusAdded', bonus);
            return bonus.id;
        },
        
        // Remove bonus
        removeBonus: function(bonusId) {
            const index = this.activeBonuses.findIndex(b => b.id === bonusId);
            if (index > -1) {
                const bonus = this.activeBonuses[index];
                this.activeBonuses.splice(index, 1);
                this.removeBonus(bonus);
                Events.emit('bonusRemoved', bonus);
            }
        },
        
        // Apply bonus effect
        applyBonus: function(bonus) {
            switch (bonus.type) {
                case this.types.MULTIPLIER:
                    this.applyMultiplierBonus(bonus.value);
                    break;
                case this.types.CASH:
                    this.applyCashBonus(bonus.value);
                    break;
                case this.types.FREE_SPINS:
                    this.applyFreeSpinsBonus(bonus.value);
                    break;
            }
        },
        
        // Apply multiplier bonus
        applyMultiplierBonus: function(multiplier) {
            if (window.TowerRushGame && window.TowerRushGame.updateMultiplier) {
                const currentMultiplier = this.getCurrentMultiplier();
                window.TowerRushGame.updateMultiplier(currentMultiplier * multiplier);
            }
            Utils.showNotification(`${multiplier}x Multiplier Bonus Activated!`, 'success');
        },
        
        // Apply cash bonus
        applyCashBonus: function(amount) {
            if (window.TowerRushGame && window.TowerRushGame.updateBalance) {
                const currentBalance = this.getCurrentBalance();
                window.TowerRushGame.updateBalance(currentBalance + amount);
            }
            Utils.showNotification(`+$${Utils.formatNumber(amount)} Bonus!`, 'success');
        },
        
        // Apply free spins bonus
        applyFreeSpinsBonus: function(spins) {
            // Store free spins in localStorage
            const currentSpins = Utils.storage.get('freeSpins', 0);
            Utils.storage.set('freeSpins', currentSpins + spins);
            Utils.showNotification(`+${spins} Free Spins!`, 'success');
        },
        
        // Get current multiplier
        getCurrentMultiplier: function() {
            return this.activeBonuses
                .filter(b => b.type === this.types.MULTIPLIER)
                .reduce((total, bonus) => total * bonus.value, 1);
        },
        
        // Get current balance
        getCurrentBalance: function() {
            const balanceElement = document.getElementById('game-balance');
            if (balanceElement) {
                return parseInt(balanceElement.textContent.replace(/,/g, '')) || 0;
            }
            return 0;
        },
        
        // Check for daily bonus
        checkDailyBonus: function() {
            const lastBonus = Utils.storage.get('lastDailyBonus', 0);
            const now = Date.now();
            const oneDay = 24 * 60 * 60 * 1000;
            
            if (now - lastBonus > oneDay) {
                this.grantDailyBonus();
                Utils.storage.set('lastDailyBonus', now);
            }
        },
        
        // Grant daily bonus
        grantDailyBonus: function() {
            const bonusAmount = Utils.random(10000, 50000);
            this.addBonus(this.types.CASH, bonusAmount);
            Utils.showNotification('Daily Bonus Claimed!', 'success');
        },
        
        // Random bonus chance
        triggerRandomBonus: function() {
            const chance = Math.random();
            
            if (chance < 0.1) { // 10% chance
                const bonusType = Math.random();
                
                if (bonusType < 0.4) {
                    // Multiplier bonus
                    const multiplier = Utils.random(2, 5);
                    this.addBonus(this.types.MULTIPLIER, multiplier, 30000); // 30 seconds
                } else if (bonusType < 0.8) {
                    // Cash bonus
                    const amount = Utils.random(5000, 25000);
                    this.addBonus(this.types.CASH, amount);
                } else {
                    // Free spins bonus
                    const spins = Utils.random(1, 3);
                    this.addBonus(this.types.FREE_SPINS, spins);
                }
            }
        },
        
        // Initialize bonus system
        init: function() {
            // Check for daily bonus on load
            this.checkDailyBonus();
            
            // Set up random bonus triggers
            Events.on('gamePlay', () => {
                this.triggerRandomBonus();
            });
            
            console.log('Bonus system initialized');
        }
    };
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            BonusSystem.init();
        });
    } else {
        BonusSystem.init();
    }
    
    // Export to global scope
    window.BonusSystem = BonusSystem;
    
})();
